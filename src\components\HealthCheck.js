import React, { useState } from 'react';

const HealthCheck = () => {
    const [result, setResult] = useState('Click the button to test...');

    const checkHealth = async () => {
        try {
            const res = await fetch('http://localhost:8000/health');
            const data = await res.json();
            setResult(JSON.stringify(data, null, 2));
        } catch (error) {
            setResult('Error: ' + error.message);
        }
    };

    return (
        <div className="card">
            <h2>Health Check</h2>
            <button onClick={checkHealth}>Check API</button>
            <pre>{result}</pre>
        </div>
    );
};

export default HealthCheck;
