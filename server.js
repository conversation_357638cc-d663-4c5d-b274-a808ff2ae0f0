const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 8000;

// Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'OK', message: 'API is running', timestamp: new Date().toISOString() });
});

// Model info endpoint
app.get('/model/info', (req, res) => {
    res.json({
        model_name: 'Credit Scoring Model',
        version: '1.0.0',
        algorithm: 'Random Forest',
        features: ['age', 'income', 'loan', 'credit_history', 'employment_status'],
        target: 'credit_score'
    });
});

// Prediction endpoint
app.post('/predict', (req, res) => {
    const { age, income, loan } = req.body;
    
    // Simple prediction logic (mock)
    const score = Math.min(850, Math.max(300, 
        Math.floor(300 + (age * 2) + (income / 1000) - (loan / 100))
    ));
    
    const risk = score < 600 ? 'High' : score < 700 ? 'Medium' : 'Low';
    
    res.json({
        prediction: score,
        risk_level: risk,
        input_data: req.body,
        confidence: Math.random() * 0.2 + 0.8 // Random confidence between 0.8-1.0
    });
});

// Feature importance endpoint
app.get('/features/importance', (req, res) => {
    res.json({
        features: [
            { feature: 'income', importance: 0.35 },
            { feature: 'credit_history', importance: 0.25 },
            { feature: 'age', importance: 0.20 },
            { feature: 'employment_status', importance: 0.15 },
            { feature: 'loan', importance: 0.05 }
        ]
    });
});

app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});
